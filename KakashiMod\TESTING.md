# Kakashi Mod Testing Guide

This guide helps you test the Kakashi Invincibility Mod to ensure it's working correctly.

## Pre-Testing Setup

1. **Install the mod** following the README instructions
2. **Enable debug mode** in `config.lua`:
   ```lua
   enable_debug = true,
   ```
3. **Launch Palworld** and check the UE4SS console for initialization messages

## Expected Console Messages

When the mod loads correctly, you should see:
```
[KakashiMod] Kakashi Invincibility Mod initialized
[KakashiMod] Kakashi Invincibility Mod loaded successfully!
[KakashiMod] Commands: /Kakashi On | /Kakashi Off
```

## Test Cases

### Test 1: Basic Command Recognition

1. **Join/Create a world**
2. **Type in chat**: `/Kakashi On`
3. **Expected result**: Console shows:
   ```
   [KakashiMod] Processing chat command: /Kakashi On
   [KakashiMod] Kakashi mode activated for player
   [KakashiMod] Kakashi mode activated! You are now invincible.
   ```

### Test 2: Invincibility Activation

1. **Activate Kakashi mode**: `/Kakashi On`
2. **Check your stats**: Health and Shield should be at 999,999
3. **Take damage**: Attack yourself with a weapon or let enemies attack
4. **Expected result**: 
   - No health/shield loss
   - Console shows: `[KakashiMod] Blocked damage for Kakashi player: [UID]`

### Test 3: Different Damage Types

Test invincibility against various damage sources:
- **Melee weapons** (swords, clubs)
- **Ranged weapons** (guns, bows)
- **Environmental damage** (fire, poison)
- **Fall damage** (jump from heights)
- **Pal attacks** (let wild Pals attack you)

**Expected result**: No damage from any source

### Test 4: Deactivation

1. **While invincible**, type: `/Kakashi Off`
2. **Expected result**: 
   - Console shows deactivation messages
   - Health/Shield return to normal values
   - You can take damage again

### Test 5: Multiple Players (Multiplayer)

If testing on a server:
1. **Have multiple players** use the commands
2. **Each player should have independent** Kakashi status
3. **Test that one player's status** doesn't affect others

## Troubleshooting Tests

### Test: Command Case Sensitivity

- Try: `/kakashi on` (lowercase)
- Try: `/KAKASHI ON` (uppercase)
- **Expected**: Only exact case should work (configurable)

### Test: Partial Commands

- Try: `/Kakashi` (incomplete)
- Try: `/Kakashi Enable` (wrong word)
- **Expected**: No effect, no error messages

### Test: Rapid Toggle

1. **Quickly alternate**: `/Kakashi On` → `/Kakashi Off` → `/Kakashi On`
2. **Expected**: Each command should work correctly without conflicts

## Performance Tests

### Test: Health Maintenance Loop

1. **Activate Kakashi mode**
2. **Monitor console** for health maintenance messages (if debug enabled)
3. **Expected**: Regular health/shield restoration every second

### Test: Memory Usage

1. **Activate/deactivate multiple times**
2. **Check for memory leaks** (original stats should be properly stored/restored)
3. **Expected**: No accumulation of stored data

## Debug Information

With debug mode enabled, you should see detailed logs:

```
[KakashiMod] Processing chat command: /Kakashi On
[KakashiMod] Stored original stats for player: [UID]
[KakashiMod] Applied invincibility to player: [UID]
[KakashiMod] Blocked damage for Kakashi player: [UID]
[KakashiMod] Removed invincibility from player: [UID]
[KakashiMod] Restored original stats for player: [UID]
```

## Common Issues and Solutions

### Issue: Commands not recognized
- **Check**: Chat message format exactly matches config
- **Check**: Mod is properly loaded in mods.txt

### Issue: Damage still taken
- **Check**: Player UID is correctly identified
- **Check**: Damage hooks are properly registered

### Issue: Health not maintained
- **Check**: LoopAsync is running (debug messages)
- **Check**: Player object is valid and accessible

### Issue: Stats not restored
- **Check**: Original stats were properly stored
- **Check**: Player UID consistency

## Validation Checklist

- [ ] Mod loads without errors
- [ ] Commands are recognized in chat
- [ ] Invincibility prevents all damage types
- [ ] Health/Shield set to maximum values
- [ ] Deactivation restores original stats
- [ ] Multiple activations/deactivations work
- [ ] Debug messages appear when enabled
- [ ] No console errors during normal operation

## Reporting Issues

When reporting issues, include:
1. **Console output** with debug enabled
2. **Exact steps** to reproduce the problem
3. **Game version** and UE4SS version
4. **Other mods** installed (potential conflicts)
5. **Single player or multiplayer** environment
