@echo off
echo ========================================
echo Kakashi Invincibility Mod Installer
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running as administrator...
) else (
    echo Warning: Not running as administrator. You may need admin rights to copy files.
    echo.
)

REM Try to find Palworld installation
set "PALWORLD_PATH="
set "STEAM_PATH=C:\Program Files (x86)\Steam\steamapps\common\Palworld\Pal\Binaries\Win64"
set "STEAM_PATH2=C:\Steam\steamapps\common\Palworld\Pal\Binaries\Win64"

if exist "%STEAM_PATH%" (
    set "PALWORLD_PATH=%STEAM_PATH%"
    echo Found Palworld at: %STEAM_PATH%
) else if exist "%STEAM_PATH2%" (
    set "PALWORLD_PATH=%STEAM_PATH2%"
    echo Found Palworld at: %STEAM_PATH2%
) else (
    echo Could not automatically find Palworld installation.
    echo Please enter the path to your Palworld\Pal\Binaries\Win64 folder:
    set /p "PALWORLD_PATH=Path: "
)

echo.

REM Check if UE4SS is installed
if not exist "%PALWORLD_PATH%\UE4SS.dll" (
    echo ERROR: UE4SS not found in Palworld directory!
    echo Please install UE4SS first from:
    echo https://github.com/Okaetsu/RE-UE4SS/releases/tag/experimental-palworld
    echo.
    pause
    exit /b 1
)

echo UE4SS found. Proceeding with mod installation...
echo.

REM Create Mods directory if it doesn't exist
if not exist "%PALWORLD_PATH%\Mods" (
    mkdir "%PALWORLD_PATH%\Mods"
    echo Created Mods directory.
)

REM Copy mod files
echo Copying Kakashi Mod files...
xcopy /E /I /Y "Scripts" "%PALWORLD_PATH%\Mods\KakashiMod\Scripts\"
copy /Y "README.md" "%PALWORLD_PATH%\Mods\KakashiMod\"
copy /Y "TESTING.md" "%PALWORLD_PATH%\Mods\KakashiMod\"

if %errorLevel% == 0 (
    echo Mod files copied successfully!
) else (
    echo ERROR: Failed to copy mod files. Check permissions.
    pause
    exit /b 1
)

REM Update mods.txt
set "MODS_FILE=%PALWORLD_PATH%\Mods\mods.txt"
echo.
echo Updating mods.txt...

REM Check if mods.txt exists
if not exist "%MODS_FILE%" (
    echo Creating mods.txt...
    echo KakashiMod : 1 > "%MODS_FILE%"
) else (
    REM Check if KakashiMod is already in mods.txt
    findstr /C:"KakashiMod" "%MODS_FILE%" >nul
    if %errorLevel% == 0 (
        echo KakashiMod already exists in mods.txt
    ) else (
        echo Adding KakashiMod to mods.txt...
        echo KakashiMod : 1 >> "%MODS_FILE%"
    )
)

echo.
echo ========================================
echo Installation Complete!
echo ========================================
echo.
echo Mod installed to: %PALWORLD_PATH%\Mods\KakashiMod\
echo.
echo To use the mod:
echo 1. Launch Palworld
echo 2. Join or create a world
echo 3. Type "/Kakashi On" in chat to activate invincibility
echo 4. Type "/Kakashi Off" in chat to deactivate
echo.
echo For troubleshooting, see TESTING.md in the mod folder.
echo.
pause
