# Kakashi Invincibility Mod for Palworld

A UE4SS-based mod that provides invincibility commands for Palworld players, inspired by the legendary ninja <PERSON><PERSON><PERSON>.

## Features

- **Complete Invincibility**: Become immune to all types of damage in Palworld
- **Chat Commands**: Simple `/<PERSON><PERSON><PERSON> On` and `/<PERSON><PERSON><PERSON> Off` commands
- **Max Health & Defense**: Sets maximum health, shield, and defense values
- **Configurable**: Easily customize commands and values through config file
- **Debug Support**: Optional debug logging for troubleshooting

## Requirements

- **Palworld** (Latest version)
- **UE4SS** (Experimental Palworld version) - Download from: https://github.com/Okaetsu/RE-UE4SS/releases/tag/experimental-palworld

## Installation

### Step 1: Install UE4SS

1. Download `UE4SS-Palworld.zip` from the link above
2. Extract the contents to your Palworld installation directory:
   ```
   C:\Program Files (x86)\Steam\steamapps\common\Palworld\Pal\Binaries\Win64\
   ```
3. You should have these files in the Win64 folder:
   - `dwmapi.dll`
   - `UE4SS.dll`
   - `UE4SS-settings.ini`
   - `Mods/` folder

### Step 2: Configure UE4SS

1. Open `UE4SS-settings.ini` in a text editor
2. Ensure these settings are configured:
   ```ini
   ConsoleEnabled = 0
   GuiConsoleEnabled = 1
   GuiConsoleVisible = 1
   EnableHotReloadSystem = 1
   ```
3. If you experience a white screen, change `GraphicsAPI = dx11`

### Step 3: Install Kakashi Mod

1. Copy the entire `KakashiMod` folder to:
   ```
   C:\Program Files (x86)\Steam\steamapps\common\Palworld\Pal\Binaries\Win64\Mods\
   ```
2. Your folder structure should look like:
   ```
   Mods/
   ├── KakashiMod/
   │   ├── Scripts/
   │   │   ├── main.lua
   │   │   ├── config.lua
   │   │   └── mod.lua
   │   └── README.md
   ```

### Step 4: Enable the Mod

1. Navigate to the `Mods` folder
2. Open `mods.txt` in a text editor
3. Add this line:
   ```
   KakashiMod : 1
   ```
4. Save the file

## Usage

### Commands

- **`/Kakashi On`** - Activates invincibility mode
  - Sets maximum health and shield to 999,999
  - Makes you immune to all damage types
  - Provides maximum defense values

- **`/Kakashi Off`** - Deactivates invincibility mode
  - Restores your original health and shield values
  - Returns you to normal vulnerability

### In-Game Usage

1. Launch Palworld
2. Open the UE4SS console (should appear automatically)
3. Join or create a world
4. Type `/Kakashi On` in the chat to become invincible
5. Type `/Kakashi Off` in the chat to return to normal

## Configuration

You can customize the mod by editing `KakashiMod/Scripts/config.lua`:

```lua
-- Command settings
command_on = "/Kakashi On",      -- Change the activation command
command_off = "/Kakashi Off",    -- Change the deactivation command

-- Invincibility settings
max_health = 999999.0,           -- Maximum health when invincible
max_shield = 999999.0,           -- Maximum shield when invincible
damage_multiplier = 0.0,         -- Damage multiplier (0.0 = no damage)

-- Defense settings
physical_defense = 999999.0,     -- Physical defense value
elemental_defense = 999999.0,    -- Elemental defense value

-- Debug settings
enable_debug = false,            -- Enable debug messages
```

## Troubleshooting

### Mod Not Loading
- Check that `mods.txt` contains `KakashiMod : 1`
- Verify UE4SS is properly installed and configured
- Check the UE4SS console for error messages

### Commands Not Working
- Ensure you're typing the exact command (case-sensitive)
- Check that the mod loaded successfully in the console
- Try enabling debug mode in config.lua for more information

### Game Crashes
- Make sure you're using the correct UE4SS version for Palworld
- Try disabling other mods to check for conflicts
- Verify your Palworld game files through Steam

### White Screen on Launch
- Change `GraphicsAPI = dx11` in `UE4SS-settings.ini`
- Try running the game in windowed mode

## Compatibility

- **Single Player**: Fully supported
- **Multiplayer**: Should work on dedicated servers with UE4SS
- **Game Updates**: May require mod updates after Palworld patches

## Disclaimer

- Use this mod at your own risk
- This mod is for entertainment purposes only
- Not recommended for competitive multiplayer servers
- May affect game balance and progression

## Support

If you encounter issues:
1. Check the troubleshooting section above
2. Enable debug mode in config.lua
3. Check the UE4SS console for error messages
4. Join the Palworld Modding Discord for community support

## Credits

- Based on UE4SS by the UE4SS-RE team
- Inspired by Teh's Lua Modding Tutorial
- Created for the Palworld modding community

## License

This mod is provided as-is for educational and entertainment purposes.
