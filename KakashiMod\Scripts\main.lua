-- Ka<PERSON>hi Invincibility Mod for Palworld
-- Main script file

local config = require("config")

-- Global variables
local kakashi_players = {}  -- Track players with Kakashi mode enabled
local original_stats = {}   -- Store original player stats for restoration

-- Utility functions
local function log_debug(message)
    if config.enable_debug then
        print("[KakashiMod] " .. message)
    end
end

local function log_info(message)
    print("[KakashiMod] " .. message)
end

-- Function to get player from chat message context
local function get_player_from_chat(chat_message)
    local sender_uid = chat_message.SenderPlayerUId
    if not sender_uid then
        return nil
    end
    
    -- Find the player character by UID
    local players = FindAllOf("PalPlayerCharacter")
    if not players then
        return nil
    end
    
    for _, player in pairs(players) do
        if player and player:IsValid() then
            local player_state = player:GetPlayerState()
            if player_state and player_state:IsValid() then
                local player_uid = player_state:GetPlayerUId()
                if player_uid and player_uid:ToString() == sender_uid:ToString() then
                    return player
                end
            end
        end
    end
    
    return nil
end

-- Function to apply invincibility to a player
local function apply_invincibility(player)
    if not player or not player:IsValid() then
        log_debug("Invalid player object for invincibility")
        return false
    end
    
    local player_uid = player:GetPlayerState():GetPlayerUId():ToString()
    
    -- Store original stats if not already stored
    if not original_stats[player_uid] then
        original_stats[player_uid] = {
            max_hp = player.MaxHP:get(),
            max_shield = player.MaxShield:get(),
        }
        log_debug("Stored original stats for player: " .. player_uid)
    end
    
    -- Apply invincibility settings
    player.MaxHP = config.max_health
    player.MaxShield = config.max_shield
    player.HP = config.max_health
    player.Shield = config.max_shield
    
    -- Mark player as having Kakashi mode
    kakashi_players[player_uid] = true
    
    log_debug("Applied invincibility to player: " .. player_uid)
    return true
end

-- Function to remove invincibility from a player
local function remove_invincibility(player)
    if not player or not player:IsValid() then
        log_debug("Invalid player object for invincibility removal")
        return false
    end
    
    local player_uid = player:GetPlayerState():GetPlayerUId():ToString()
    
    -- Restore original stats if available
    if original_stats[player_uid] then
        player.MaxHP = original_stats[player_uid].max_hp
        player.MaxShield = original_stats[player_uid].max_shield
        
        -- Set current HP/Shield to max of restored values
        player.HP = original_stats[player_uid].max_hp
        player.Shield = original_stats[player_uid].max_shield
        
        log_debug("Restored original stats for player: " .. player_uid)
    end
    
    -- Remove player from Kakashi mode
    kakashi_players[player_uid] = nil
    
    log_debug("Removed invincibility from player: " .. player_uid)
    return true
end

-- Function to handle damage prevention
local function prevent_damage(player, damage_amount)
    if not player or not player:IsValid() then
        return damage_amount
    end
    
    local player_uid = player:GetPlayerState():GetPlayerUId():ToString()
    
    if kakashi_players[player_uid] then
        log_debug("Prevented " .. tostring(damage_amount) .. " damage to Kakashi player: " .. player_uid)
        return 0  -- No damage
    end
    
    return damage_amount  -- Normal damage
end

-- Chat command handler
local function handle_chat_command(chat_message)
    local message = chat_message.Message:ToString()
    local player = get_player_from_chat(chat_message)
    
    if not player then
        log_debug("Could not find player for chat command")
        return
    end
    
    log_debug("Processing chat command: " .. message)
    
    -- Check for Kakashi On command
    if message == config.command_on then
        if apply_invincibility(player) then
            log_info("Kakashi mode activated for player")
            if config.enable_chat_feedback then
                -- Note: Direct chat response would require additional implementation
                -- For now, we'll log to console
                log_info(config.feedback_on)
            end
        end
    
    -- Check for Kakashi Off command
    elseif message == config.command_off then
        if remove_invincibility(player) then
            log_info("Kakashi mode deactivated for player")
            if config.enable_chat_feedback then
                log_info(config.feedback_off)
            end
        end
    end
end

-- Hook into chat messages to detect commands
RegisterHook("/Script/Pal.PalGameStateInGame:BroadcastChatMessage", function(self, ChatMessage)
    local chat_message = ChatMessage:get()
    handle_chat_command(chat_message)
end)

-- Hook into damage system to prevent damage for Kakashi players
RegisterHook("/Script/Pal.PalCharacter:TakeDamage", function(self, DamageAmount, DamageEvent, EventInstigator, DamageCauser)
    -- Check if this is a player character
    if self:IsA("/Script/Pal.PalPlayerCharacter") then
        local player_state = self:GetPlayerState()
        if player_state and player_state:IsValid() then
            local player_uid = player_state:GetPlayerUId():ToString()

            if kakashi_players[player_uid] then
                -- Prevent damage by setting damage to 0
                DamageAmount:set(0.0)
                log_debug("Blocked damage for Kakashi player: " .. player_uid)
            end
        end
    end
end)

-- Additional damage prevention hooks for different damage types
RegisterHook("/Script/Pal.PalPlayerCharacter:ReceiveAnyDamage", function(self, Damage, DamageType, InstigatedBy, DamageCauser)
    local player_state = self:GetPlayerState()
    if player_state and player_state:IsValid() then
        local player_uid = player_state:GetPlayerUId():ToString()

        if kakashi_players[player_uid] then
            -- Prevent any damage
            Damage:set(0.0)
            log_debug("Blocked any damage for Kakashi player: " .. player_uid)
        end
    end
end)

-- Hook to maintain health and shield at maximum for Kakashi players
LoopAsync(1000, function()
    for player_uid, _ in pairs(kakashi_players) do
        local players = FindAllOf("PalPlayerCharacter")
        if players then
            for _, player in pairs(players) do
                if player and player:IsValid() then
                    local player_state = player:GetPlayerState()
                    if player_state and player_state:IsValid() then
                        local current_uid = player_state:GetPlayerUId():ToString()
                        if current_uid == player_uid then
                            -- Maintain maximum health and shield
                            player.HP = config.max_health
                            player.Shield = config.max_shield
                            break
                        end
                    end
                end
            end
        end
    end
end)

-- Initialize mod
RegisterHook("/Script/Engine.PlayerController:ServerAcknowledgePossession", function(Context)
    log_info("Kakashi Invincibility Mod loaded successfully!")
    log_info("Commands: " .. config.command_on .. " | " .. config.command_off)
end)

log_info("Kakashi Invincibility Mod initialized")
